<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑政策' : '新增政策'"
    width="1000px"
    class="policy-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="policy-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="政策名称" prop="info.policyName">
            <el-input
              v-model="formData.info.policyName"
              placeholder="请输入政策名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布部门" prop="info.publishDepartment">
            <el-input
              v-model="formData.info.publishDepartment"
              placeholder="请输入发布部门"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="发布日期" prop="info.publishDate">
            <el-date-picker
              v-model="formData.info.publishDate"
              type="date"
              placeholder="选择发布日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实施日期" prop="info.implementationDate">
            <el-date-picker
              v-model="formData.info.implementationDate"
              type="date"
              placeholder="选择实施日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="失效日期" prop="info.expiryDate">
            <el-date-picker
              v-model="formData.info.expiryDate"
              type="date"
              placeholder="选择失效日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="政策层级" prop="info.policyLevel">
            <el-select
              v-model="formData.info.policyLevel"
              placeholder="请选择政策层级"
              style="width: 100%"
            >
              <el-option
                v-for="item in policyLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="政策类型" prop="info.policyType">
            <el-select
              v-model="formData.info.policyType"
              placeholder="请选择政策类型"
              style="width: 100%"
            >
              <el-option
                v-for="item in policyTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="适用地区" prop="info.applicableRegion">
            <el-cascader
              v-model="formData.info.applicableRegion"
              placeholder="请选择适用地区"
              :options="applicableRegionOptions"
              filterable
              clearable
              :props="{
                multiple: true,
                value: 'code',
                label: 'name',
                children: 'children',
                // 父子节点不强制关联选中（多选更灵活）
                checkStrictly: true,
                emitPath: false,
              }"
              style="width: 100%"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="政策编号" prop="info.policyNumber">
            <el-input
              v-model="formData.info.policyNumber"
              placeholder="请输入政策编号"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="核心主题" prop="info.coreTheme">
            <el-input
              v-model="formData.info.coreTheme"
              placeholder="请输入核心主题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="涉及污染物" prop="info.controlledPollutants">
            <el-select
              v-model="formData.info.controlledPollutants"
              placeholder="请选择涉及污染物"
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in pollutantOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管控对象" prop="info.controlTargets">
            <el-input
              v-model="formData.info.controlTargets"
              placeholder="请输入管控对象"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="数据状态" prop="info.dataStatus">
            <el-select
              v-model="formData.info.dataStatus"
              placeholder="请选择数据状态"
              style="width: 100%"
            >
              <el-option
                v-for="item in dataStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>

      <el-form-item label="备注" prop="info.remarks">
        <el-input
          v-model="formData.info.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="附件上传" prop="fileList.addFileIds" required>
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :file-list="fileList"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          multiple
          drag
          class="upload-area"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">
              支持上传 PDF、DOC、DOCX、XLS、XLSX 等格式文件，单个文件不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="handleSaveDraft" :loading="saving"> 保存草稿 </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadFiles } from 'element-plus'
import { policyApi } from '@/api/knowledge'
// import type { PolicyFormData } from '@/api/policy'
import { config } from '@/config/env'
import { fetchDictDataByTypes } from '@/utils/options'
import { getDistrict } from '@/api/common'
// 定义组件属性
interface Props {
  visible: boolean
  isEdit?: boolean
  editData?: any
}

// 定义事件
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}
const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  editData: null,
})
const emit = defineEmits<Emits>()

// 字典数据
const policyLevelOptions = ref([] as any[])
const policyTypeOptions = ref([] as any[])
const applicableRegionOptions = ref([] as any[])
const pollutantOptions = ref([] as any[])
const fetchDataStatus = async () => {}
fetchDataStatus()
const initDictData = async () => {
  const dictTypeMap = {
    policyLevel: 'zccj',
    policyType: 'zclx',
    controlledPollutants: 'sjwrw',
  }
  // 获取字典数据
  const dictData = await fetchDictDataByTypes(dictTypeMap)
  policyLevelOptions.value = dictData.policyLevel
  policyTypeOptions.value = dictData.policyType
  pollutantOptions.value = dictData.controlledPollutants
  try {
    const res = await getDistrict({ code: '' })
    applicableRegionOptions.value = res
  } catch (error) {
    console.error('获取地区数据失败:', error)
  }
  Object.assign(dictOptions, dictData, {
    applicableRegion: applicableRegionOptions.value,
  })
}
const dictOptions = reactive<{ [key: string]: any[] }>({
  policyLevel: [] as any[],
  policyType: [] as any[],
  controlledPollutants: [] as any[],

  applicableRegion: [] as any[],
})
initDictData()

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const saving = ref(false)
const submitting = ref(false)
const fileList = ref<UploadFile[]>([])

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 表单数据
const formData = reactive({
  info: {
    policyName: '',
    publishDepartment: '',
    publishDate: '',
    implementationDate: '',
    expiryDate: '',
    policyLevel: '',
    policyType: '',
    applicableRegion: [] as string[],
    policyNumber: '',
    coreTheme: '',
    controlledPollutants: [] as string[],
    controlTargets: '',
    remarks: '',
  },
  fileList: {
    addFileIds: [] as string[],
    delFileIds: [] as string[],
  },
})
// 自定义验证函数
const validateFileList = (rule: any, value: string[], callback: Function) => {
  if (formData.fileList.addFileIds.length === 0) {
    callback(new Error('请至少上传一个附件'))
  } else {
    callback()
  }
}
// 表单验证规则
const formRules: FormRules = {
  'info.policyName': [{ required: true, message: '请输入政策名称', trigger: 'blur' }],
  'info.publishDepartment': [{ required: true, message: '请输入发布部门', trigger: 'blur' }],
  'info.publishDate': [{ required: true, message: '请选择发布日期', trigger: 'change' }],
  'info.implementationDate': [{ required: true, message: '请选择实施日期', trigger: 'change' }],
  'info.expiryDate': [{ required: true, message: '请选择失效日期', trigger: 'change' }],
  'info.policyLevel': [{ required: true, message: '请选择政策层级', trigger: 'change' }],
  'info.policyType': [{ required: true, message: '请选择政策类型', trigger: 'change' }],
  'info.policyNumber': [{ required: true, message: '请输入政策编号', trigger: 'blur' }],
  'info.controlledPollutants': [{ required: true, message: '请选择涉及污染物', trigger: 'change' }],
  'info.applicableRegion': [{ required: true, message: '请选择适用地区', trigger: 'change' }],
  'info.controlTargets': [{ required: true, message: '请输入管控对象', trigger: 'blur' }],
  'fileList.addFileIds': [{ required: true, validator: validateFileList, trigger: 'change' }],
}

// 文件上传配置
const uploadAction = config.baseURL + '/File/upload'
// const uploadAction = 'http://192.168.123.15:10054' + '/File/upload'
const uploadHeaders = {
  Authorization: 'Bearer ' + (localStorage.getItem('token') || ''),
}
const uploadData = {
  type: 'policyInfo',
}

// 监听编辑数据变化
watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      resetForm()
    }
  },
)
watch(
  () => props.editData,
  (newData) => {
    if (newData && props.isEdit) {
      Object.assign(formData.info, newData)
      // 如果有附件数据，也需要处理
      if (newData.fileList) {
        formData.fileList.addFileIds = newData.fileList.map((file: any) => file.fileId)
        fileList.value = newData.fileList.map((file: any) => ({
          name: file.fileName,
          url: file.fileUrl,
          uid: file.fileId,
        }))
      }
    }
  },
  { immediate: true },
)

// 重置表单
const resetForm = () => {
  Object.assign(formData.info, {
    policyName: '',
    publishDepartment: '',
    publishDate: '',
    implementationDate: '',
    expiryDate: '',
    policyLevel: '',
    applicableRegion: [],
    policyType: '',
    policyNumber: '',
    coreTheme: '',
    controlledPollutants: [],
    controlTargets: '',
    remarks: '',
  })
  formData.fileList.addFileIds = []
  fileList.value = []
  formRef.value?.clearValidate()
  formRef.value?.resetFields()
}

// 文件上传相关方法
const beforeUpload = (file: File) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ]

  const isAllowedType = allowedTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    ElMessage.error('只能上传 PDF、DOC、DOCX、XLS、XLSX 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any, file: UploadFile, fileList: UploadFiles) => {
  if (response.code === 200) {
    formData.fileList.addFileIds.push(response.data.fileId)
    file.response = response.data
    ElMessage.success('文件上传成功')
    formRef.value?.validateField('fileList.addFileIds')
  } else {
    ElMessage.error(response.message || '文件上传失败')
    const index = fileList.findIndex((f) => f.uid === file.uid)
    if (index > -1) {
      fileList.splice(index, 1)
    }
  }
}

const handleUploadError = (error: any) => {
  ElMessage.error('文件上传失败: ' + error.message)
}

const handleFileRemove = (file: UploadFile) => {
  console.log('%c Line:483 🥚 file', 'color:#2eafb0', file)
  // 过滤文件里面对应的uid相同的数据

  formData.fileList.delFileIds.push(String(file.uid))
  formData.fileList.addFileIds = formData.fileList.addFileIds.filter(
    (id) => id !== String(file.uid),
  )
  fileList.value = fileList.value.filter((f) => f.uid !== file.uid)
  // 如果文件有response数据，说明是上传成功的文件
  // const response = file.response as any
  // if (response && response.fileId) {
  //   const index = formData.fileList.addFileIds.findIndex((id) => id === response.fileId)
  //   if (index > -1) {
  //     formData.fileList.addFileIds.splice(index, 1)
  //   }
  // }
  // 触发表单验证
  formRef.value?.validateField('fileList.addFileIds')
}

// // 保存草稿
const handleSaveDraft = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true

    const submitData = {
      ...formData,
      info: {
        ...formData.info,
        dataStatus: 2, // 暂存状态
      },
    }

    // 调用保存接口
    await savePolicy(submitData)

    ElMessage.success('保存草稿成功')
    emit('success')
    emit('update:visible', false)
    resetForm()
  } catch (error) {
    console.error('保存草稿失败:', error)
  } finally {
    saving.value = false
  }
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    const submitData = {
      ...formData,
      info: {
        ...formData.info,
        dataStatus: 3, // 提交状态
      },
    }

    // 调用保存接口
    await savePolicy(submitData)

    ElMessage.success('提交成功')
    emit('success')
    emit('update:visible', false)
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 保存政策接口
const savePolicy = async (data: any) => {
  try {
    if (props.isEdit) {
      return await policyApi.updatePolicyInfo(data)
    } else {
      return await policyApi.savePolicyInfo(data)
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
    throw error
  }
}
</script>

<style scoped>
.policy-form-dialog {
  .el-dialog__body {
    padding: 20px 24px;
  }

  .policy-form {
    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #303133;
      }

      .el-input,
      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-textarea {
        .el-textarea__inner {
          resize: vertical;
          min-height: 80px;
        }
      }
    }

    .el-row {
      margin-bottom: 0;
    }
  }

  .upload-area {
    width: 100%;

    .el-upload {
      width: 100%;
    }

    .el-upload-dragger {
      width: 100%;
      height: 120px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      .el-icon--upload {
        font-size: 28px;
        color: #c0c4cc;
        margin-bottom: 8px;
      }

      .el-upload__text {
        color: #606266;
        font-size: 14px;

        em {
          color: #409eff;
          font-style: normal;
          text-decoration: underline;
        }
      }
    }

    .el-upload__tip {
      color: #909399;
      font-size: 12px;
      margin-top: 8px;
      line-height: 1.4;
    }

    .el-upload-list {
      margin-top: 12px;

      .el-upload-list__item {
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 8px 12px;
        margin-bottom: 8px;
        background-color: #f8f9fa;

        &:hover {
          background-color: #f0f2f5;
        }

        .el-upload-list__item-name {
          color: #606266;
          font-size: 14px;
        }

        .el-upload-list__item-status-label {
          color: #67c23a;
        }

        .el-icon--close {
          color: #c0c4cc;

          &:hover {
            color: #f56c6c;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0 0;
    border-top: 1px solid #ebeef5;

    .el-button {
      min-width: 80px;
    }
  }
}

@media (max-width: 768px) {
  .policy-form-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    .policy-form {
      .el-col {
        span: 24 !important;
      }
    }

    .dialog-footer {
      flex-direction: column;

      .el-button {
        width: 100%;
        margin: 0 0 8px 0;
      }
    }
  }
}

.el-form-item.is-error {
  .el-input__inner,
  .el-textarea__inner,
  .el-select .el-input__inner {
    border-color: #f56c6c;
  }
}

.el-form-item.is-success {
  .el-input__inner,
  .el-textarea__inner,
  .el-select .el-input__inner {
    border-color: #67c23a;
  }
}
.policy-form-dialog .el-dialog__body {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
</style>
