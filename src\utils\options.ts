import { getDict, getDistrict } from '@/api/common'

// 公共字典数据获取方法
export const fetchDictDataByTypes = async (dictTypeMap: Record<string, string>) => {
  const result: Record<string, any[]> = {}
  try {
    // 并行请求所有字典类型数据
    const promises = Object.entries(dictTypeMap).map(async ([key, type]) => {
      const res = await getDict({ type })
      // 删除第一项
      res.shift()
      result[key] = res.length > 0 ? res : []
    })
    // 等待所有请求完成
    await Promise.all(promises)
    return result
  } catch (error) {
    console.error('获取字典数据失败:', error)
    return result
  }
}
