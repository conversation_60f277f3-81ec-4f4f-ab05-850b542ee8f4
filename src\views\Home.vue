<template>
  <div class="home-page">
    <el-card class="welcome-card">
      <h1>欢迎使用环境监测系统</h1>
      <p>这是一个综合性的环境监测和数据分析平台</p>
    </el-card>
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">1,234</div>
            <div class="stat-label">监测站点</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">5,678</div>
            <div class="stat-label">数据记录</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">90%</div>
            <div class="stat-label">数据完整率</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">24/7</div>
            <div class="stat-label">实时监控</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()
</script>

<style scoped>
.home-page {
  padding: 20px;
}

.welcome-card {
  margin-bottom: 30px;
  text-align: center;
}

.welcome-card h1 {
  color: #409eff;
  margin-bottom: 10px;
}

.demo-links {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.stats-row {
  margin-top: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 10px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}
</style>
