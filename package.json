{"name": "vite2", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "dev:prod": "vite --mode production", "build": "run-p type-check \"build-only {@}\" --", "build:dev": "run-p type-check \"build-dev-only {@}\" --", "build:prod": "vite build --mode production", "build-dev-only": "vite build --mode build-dev", "preview": "vite preview", "preview:dev": "vite preview --outDir dist-dev", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.11.0", "element-plus": "^2.11.1", "lodash-es": "^4.17.21", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "node-sass": "^9.0.0", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "sass-loader": "^16.0.5", "typescript": "~5.8.0", "unplugin-auto-import": "^20.1.0", "unplugin-icons": "^22.2.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}