<template>
  <div class="page-container">
    <CollapsePanel
      title="按行数an案例库查询限制的面板"
      :show-toggle-btn="true"
      :default-max-line="1"
      default-max-height="88px"
    >
      <template #panel-button>
        <!-- <el-button size="small" type="primary">新增</el-button> -->
        <el-button size="small">编辑</el-button>
        <el-button size="small" type="primary">搜索</el-button>
        <el-button size="small" type="danger">删除</el-button>
      </template>
      <template #panel-main>
        <!-- 搜索表单 -->
        <SearchForm
          ref="searchFormRef"
          v-model="searchParams"
          :config="searchConfig"
          :first-row-count="4"
          @search="handleSearch"
          @reset="handleReset"
        />
      </template>
    </CollapsePanel>
    <CollapsePanel :title="`找到${total}条记录`" :show-toggle-btn="true">
      <template #panel-button>
        <!-- <el-button size="small" type="primary">新增</el-button> -->
        排序方式：
        <el-link type="primary">primary</el-link>
      </template>
      <template #panel-main>
        <div class="demo-content">
          <el-row :gutter="20">
            <el-col :span="6" v-for="(item, index) in gridData" :key="index">
              <el-card class="grid-item">
                <h4>{{ item.title }}</h4>
                <p>{{ item.description }}</p>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </template>
    </CollapsePanel>
    <!-- <el-card>
      <template #header>
        <div class="card-header">
          <span>{{ pageTitle }}</span>
          <el-button type="primary">添加案例</el-button>
        </div>
      </template>

      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="title" label="案例标题" width="300" />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="region" label="地区" width="120" />
        <el-table-column prop="effect" label="治理效果" width="150" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card> -->
  </div>
</template>

<script setup lang="ts">
const total = ref(100)
// 网格数据
const gridData = ref([
  { title: '项目一', description: '这是项目一的描述信息' },
  { title: '项目二', description: '这是项目二的描述信息' },
  { title: '项目三', description: '这是项目三的描述信息' },
  { title: '项目四', description: '这是项目四的描述信息' },
  { title: '项目五', description: '这是项目五的描述信息' },
  { title: '项目六', description: '这是项目六的描述信息' },
  { title: '项目七', description: '这是项目七的描述信息' },
  { title: '项目八', description: '这是项目八的描述信息' },
])
import { ref } from 'vue'
import type { FormItem } from '@/components/SearchForm.vue'

const searchFormRef = ref()

// 搜索参数
const searchParams = ref({
  warehouseName: [],
  dnNumber: '',
  outNumber: '',
  orderNumber: '',
  receiptStatus: '',
  storageStatus: '',
  podReceiptStatus: '',
  receiptCustomer: '',
  createPerson: '',
  receiptCustomerGroup: '',
  createTime: [],
  planReceiptTime: [],
  customerRequestTime: [],
  contactWay: '',
})

// 模拟API调用
const getWarehouseList = async (params: any) => {
  // 模拟异步请求
  await new Promise((resolve) => setTimeout(resolve, 300))

  return [
    { label: '北京仓库', value: 'BJ001' },
    { label: '上海仓库', value: 'SH001' },
    { label: '广州仓库', value: 'GZ001' },
    { label: '深圳仓库', value: 'SZ001' },
    { label: '杭州仓库', value: 'HZ001' },
  ].filter((item) => !params.logicWarehouseName || item.label.includes(params.logicWarehouseName))
}

// 搜索表单配置
const searchConfig: FormItem[] = [
  {
    type: 'input',
    prop: 'dnNumber',
    formItem: { label: '政策类型' },
    attrs: {
      placeholder: '请输入DN单号',
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'outNumber',
    formItem: { label: '地区' },
    attrs: {
      placeholder: '请输入出库单号',
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'orderNumber',
    formItem: { label: '发布时间' },
    attrs: {
      placeholder: '请输入销售订单号',
      clearable: true,
    },
  },
  // {
  //   type: 'select',
  //   prop: 'warehouseName',
  //   formItem: { label: '仓库名称' },
  //   attrs: {
  //     placeholder: '请选择仓库',
  //     filterable: true,
  //     remote: true,
  //     clearable: true,
  //     multiple: true,
  //     remoteMethod: async (query: string, options: any, prop: string) => {
  //       if (query) {
  //         const params = {
  //           logicWarehouseName: query,
  //           validNeed: '0',
  //           includeSupplierWarehouse: true,
  //         }
  //         const res = await getWarehouseList(params)
  //         const list = res.map((item) => ({
  //           label: `${item.label} (${item.value})`,
  //           value: item.value,
  //         }))
  //         searchFormRef.value?.setOptions(prop, list)
  //       }
  //     },
  //   },
  //   on: {
  //     focus: async () => {
  //       const params = {
  //         validNeed: '0',
  //         includeSupplierWarehouse: true,
  //       }
  //       const res = await getWarehouseList(params)
  //       const list = res.map((item) => ({
  //         label: `${item.label} (${item.value})`,
  //         value: item.value,
  //       }))
  //       searchFormRef.value?.setOptions('warehouseName', list)
  //     },
  //   },
  // },
  {
    type: 'select',
    prop: 'receiptStatus',
    formItem: { label: '收货状态' },
    attrs: {
      placeholder: '请选择收货状态',
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'storageStatus',
    formItem: { label: '储存状态' },
    attrs: {
      placeholder: '请选择储存状态',
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'podReceiptStatus',
    formItem: { label: 'POD收货状态' },
    attrs: {
      placeholder: '请选择POD收货状态',
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'receiptCustomer',
    formItem: { label: '收货客户名称' },
    attrs: {
      placeholder: '请输入收货客户名称',
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'createPerson',
    formItem: { label: '创建人' },
    attrs: {
      placeholder: '请输入创建人',
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'receiptCustomerGroup',
    formItem: { label: '收货客户组织' },
    attrs: {
      placeholder: '请输入收货客户组织',
      clearable: true,
    },
  },
  {
    type: 'daterange',
    prop: 'createTime',
    formItem: { label: '创建时间' },
    attrs: {
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    type: 'daterange',
    prop: 'planReceiptTime',
    formItem: { label: '计划送达时间' },
    attrs: {
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    type: 'daterange',
    prop: 'customerRequestTime',
    formItem: { label: '客户要求时间' },
    attrs: {
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    type: 'input',
    prop: 'contactWay',
    formItem: { label: '联系方式' },
    attrs: {
      placeholder: '请输入联系方式',
      clearable: true,
    },
  },
]

// 初始化选项数据
const initOptions = async () => {
  // 初始化状态选项
  const statusOptions = [
    { label: '待收货', value: '1' },
    { label: '已收货', value: '2' },
    { label: '部分收货', value: '3' },
  ]

  searchFormRef.value?.setOptions('receiptStatus', statusOptions)
  searchFormRef.value?.setOptions('storageStatus', statusOptions)
  searchFormRef.value?.setOptions('podReceiptStatus', statusOptions)
}

// 搜索处理
const handleSearch = (params: any) => {
  console.log('搜索参数:', params)
  // 这里可以调用API进行搜索
}

// 重置处理
const handleReset = () => {
  console.log('重置搜索条件')
}

// 组件挂载后初始化
import { onMounted } from 'vue'
onMounted(() => {
  initOptions()
})
// import { ref, computed } from 'vue'
// import { useRoute } from 'vue-router'

// const route = useRoute()

// // 根据路由显示不同的页面标题和数据
// const pageTitle = computed(() => {
//   const path = route.path
//   if (path.includes('/industrial')) {
//     return '优秀管控案例库 - 工业治理案例'
//   } else if (path.includes('/urban')) {
//     return '优秀管控案例库 - 城市治理案例'
//   } else if (path.includes('/rural')) {
//     return '优秀管控案例库 - 农村治理案例'
//   }
//   return '优秀管控案例库'
// })

// const tableData = ref([
//   {
//     id: 1,
//     title: '某工业园区VOCs综合治理案例',
//     category: 'VOCs治理',
//     region: '江苏省',
//     effect: '减排30%',
//     createTime: '2024-01-15',
//   },
//   {
//     id: 2,
//     title: '城市扬尘污染防控措施',
//     category: '扬尘治理',
//     region: '北京市',
//     effect: 'PM10降低25%',
//     createTime: '2024-01-10',
//   },
// ])

// const handleView = (row: any) => {
//   console.log('查看案例:', row)
// }

// const handleEdit = (row: any) => {
//   console.log('编辑案例:', row)
// }

// const handleDelete = (row: any) => {
//   console.log('删除案例:', row)
// }
</script>

<style scoped>
.page-container {
  /* padding: 20px; */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
