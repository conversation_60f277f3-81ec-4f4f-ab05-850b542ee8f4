import { get, post, put, del } from '@/utils/request'
import type {
  ApiResponse,
  ExcellentCase,
  PolicyInfo,
  ExcellentCaseSearchParams,
  // PolicyInfoSearchParams,
  PageResult,
  DictData,
} from './types'
export interface PolicyInfoSearchParams {
  currentPage: number
  pageSize: number
  policyName: string //政策名称（文本框）
  publishStartDate: string //发布日期（开始，区间日期选择框），yyyy-MM-dd
  publishEndDate: string //发布日期（结束，区间日期选择框），yyyy-MM-dd
  policyLevel: string //政策层级（下拉，字典zccj）
  applicableRegion: string //适用地区（下拉，适用地区字典）
  policyType: string //政策类型（下拉，字典zclx）
  policyNumber: string //政策编号（文本框）
  controlledPollutants: string //涉及污染物（下拉，字典sjwrw）
  dataStatus: string //数据状态（下拉，状态下拉）
}

export interface PolicyFormData {
  info: PolicyInfo
  fileList: {
    addFileIds: string[]
  }
}
// ==================== 政策相关API ====================
// 政策相关API
export const policyApi = {
  // 获取政策信息列表
  getPolicyInfoList: (params: PolicyInfoSearchParams) => {
    return post('/PolicyInfo/PolicyInfoList', params)
  },

  // 保存政策信息
  savePolicyInfo: (data: PolicyFormData) => {
    return post('/PolicyInfo/savePolicyInfo', data)
  },

  // // 更新政策信息
  updatePolicyInfo: (data: PolicyFormData): Promise<ApiResponse> => {
    return post('/PolicyInfo/updatePolicyInfo', data)
  },

  // 删除政策
  deletePolicyInfo: (data: { info: { policyId: string } }) => {
    return post(`/PolicyInfo/deletePolicyInfo`, data)
  },
  // 获取状态
  getDataStatusList: (): Promise<any[]> => {
    return get('/PolicyInfo/getDataStatusList')
  },
  // 审批
  approvedPolicyInfo: (data: { info: { policyId: string; dataStatus: number | string } }) => {
    return post(`/PolicyInfo/approvedPolicyInfo`, data)
  },
}

// // 获取政策信息列表
// export const getPolicyInfoList = (params: PolicyInfoSearchParams): Promise<ApiResponse> => {
//   return post<ApiResponse>('/PolicyInfo/PolicyInfoList', params)
// }

// // 获取政策信息详情
// export const getPolicyInfoDetail = (id: string | number): Promise<PolicyInfo> => {
//   return get<PolicyInfo>(`/knowledge/policies/${id}`)
// }

// // 创建政策信息
// export const createPolicyInfo = (data: Partial<PolicyInfo>): Promise<PolicyInfo> => {
//   return post<PolicyInfo>('/knowledge/policies', data)
// }

// // 更新政策信息
// export const updatePolicyInfo = (
//   id: string | number,
//   data: Partial<PolicyInfo>,
// ): Promise<PolicyInfo> => {
//   return put<PolicyInfo>(`/knowledge/policies/${id}`, data)
// }

// // 删除政策信息
// export const deletePolicyInfo = (id: string | number): Promise<void> => {
//   return del<void>(`/knowledge/policies/${id}`)
// }

// // 批量删除政策信息
// export const batchDeletePolicyInfo = (ids: (string | number)[]): Promise<void> => {
//   return del<void>('/knowledge/policies/batch', { ids })
// }

// // 获取政策类型列表
// export const getPolicyTypes = (): Promise<DictData[]> => {
//   return get<DictData[]>('/knowledge/policies/types')
// }
// // ==================== 优秀管控案例库 ====================

// // 获取优秀案例列表
// export const getExcellentCaseList = (
//   params: ExcellentCaseSearchParams,
// ): Promise<PageResult<ExcellentCase>> => {
//   return get<PageResult<ExcellentCase>>('/knowledge/excellent-cases', params)
// }

// // 获取优秀案例详情
// export const getExcellentCaseDetail = (id: string | number): Promise<ExcellentCase> => {
//   return get<ExcellentCase>(`/knowledge/excellent-cases/${id}`)
// }

// // 创建优秀案例
// export const createExcellentCase = (data: Partial<ExcellentCase>): Promise<ExcellentCase> => {
//   return post<ExcellentCase>('/knowledge/excellent-cases', data)
// }

// // 更新优秀案例
// export const updateExcellentCase = (
//   id: string | number,
//   data: Partial<ExcellentCase>,
// ): Promise<ExcellentCase> => {
//   return put<ExcellentCase>(`/knowledge/excellent-cases/${id}`, data)
// }

// // 删除优秀案例
// export const deleteExcellentCase = (id: string | number): Promise<void> => {
//   return del<void>(`/knowledge/excellent-cases/${id}`)
// }

// // 批量删除优秀案例
// export const batchDeleteExcellentCases = (ids: (string | number)[]): Promise<void> => {
//   return del<void>('/knowledge/excellent-cases/batch', { ids })
// }

// // 获取案例分类列表
// export const getCaseCategories = (): Promise<DictData[]> => {
//   return get<DictData[]>('/knowledge/excellent-cases/categories')
// }

// // ==================== 总量减排核算指南库 ====================

// // 获取减排指南列表
// export const getReductionGuideList = (params: any): Promise<PageResult<any>> => {
//   return get<PageResult<any>>('/knowledge/reduction-guides', params)
// }

// // 获取减排指南详情
// export const getReductionGuideDetail = (id: string | number): Promise<any> => {
//   return get<any>(`/knowledge/reduction-guides/${id}`)
// }

// // 创建减排指南
// export const createReductionGuide = (data: any): Promise<any> => {
//   return post<any>('/knowledge/reduction-guides', data)
// }

// // 更新减排指南
// export const updateReductionGuide = (id: string | number, data: any): Promise<any> => {
//   return put<any>(`/knowledge/reduction-guides/${id}`, data)
// }

// // 删除减排指南
// export const deleteReductionGuide = (id: string | number): Promise<void> => {
//   return del<void>(`/knowledge/reduction-guides/${id}`)
// }

// // ==================== 排查案例库 ====================

// // 获取排查案例列表
// export const getInvestigationCaseList = (params: any): Promise<PageResult<any>> => {
//   return get<PageResult<any>>('/knowledge/investigation-cases', params)
// }

// // 获取排查案例详情
// export const getInvestigationCaseDetail = (id: string | number): Promise<any> => {
//   return get<any>(`/knowledge/investigation-cases/${id}`)
// }

// // 创建排查案例
// export const createInvestigationCase = (data: any): Promise<any> => {
//   return post<any>('/knowledge/investigation-cases', data)
// }

// // 更新排查案例
// export const updateInvestigationCase = (id: string | number, data: any): Promise<any> => {
//   return put<any>(`/knowledge/investigation-cases/${id}`, data)
// }

// // 删除排查案例
// export const deleteInvestigationCase = (id: string | number): Promise<void> => {
//   return del<void>(`/knowledge/investigation-cases/${id}`)
// }

// // ==================== 特征组分来源库 ====================

// // 获取特征组分来源列表
// export const getComponentSourceList = (params: any): Promise<PageResult<any>> => {
//   return get<PageResult<any>>('/knowledge/component-sources', params)
// }

// // 获取特征组分来源详情
// export const getComponentSourceDetail = (id: string | number): Promise<any> => {
//   return get<any>(`/knowledge/component-sources/${id}`)
// }

// // 创建特征组分来源
// export const createComponentSource = (data: any): Promise<any> => {
//   return post<any>('/knowledge/component-sources', data)
// }

// // 更新特征组分来源
// export const updateComponentSource = (id: string | number, data: any): Promise<any> => {
//   return put<any>(`/knowledge/component-sources/${id}`, data)
// }

// // 删除特征组分来源
// export const deleteComponentSource = (id: string | number): Promise<void> => {
//   return del<void>(`/knowledge/component-sources/${id}`)
// }

// // ==================== 通用接口 ====================

// // 获取地区列表
// export const getRegionList = (): Promise<DictData[]> => {
//   return get<DictData[]>('/common/regions')
// }

// // 获取标签列表
// export const getTagList = (type?: string): Promise<DictData[]> => {
//   return get<DictData[]>('/common/tags', { type })
// }

// // 文件上传
// export const uploadFile = (file: File): Promise<{ url: string; filename: string }> => {
//   const formData = new FormData()
//   formData.append('file', file)
//   return post<{ url: string; filename: string }>('/common/upload', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   } as any)
// }
